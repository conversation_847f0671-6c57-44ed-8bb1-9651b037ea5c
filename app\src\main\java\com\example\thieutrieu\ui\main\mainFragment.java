package com.example.thieutrieu.ui.main;

import com.example.thieutrieu.data.model.Task;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class mainFragment {
    public List<Task> getTaskForToday(){
        List<Task> allTasks = getAllTask();
        List<Task> todayTask = new ArrayList<>();

        String today = getTodayDate();

        for(Task task : allTasks){
            if(task.getStartDate() != null){
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
                String taskDate = sdf.format(task.getStartDate());
                if(taskDate.equals(today)){
                    todayTask.add(task);
                }
            }
        }
        return todayTask;
    }

    public String getTodayDate(){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        return sdf.format(new Date());
    }

    public List<Task> getAllTask(){
        List<Task> list = new ArrayList<>();
        list.add(new Task("Cuu lay am nhac",new Date(), null, null, null, "giai oan cho Jack", "work",false));

        return list;
    }
}
