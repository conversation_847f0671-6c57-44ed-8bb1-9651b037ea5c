package com.example.thieutrieu.ui.main;

import android.os.Bundle;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.example.thieutrieu.R;
import com.example.thieutrieu.data.model.Task;
import com.example.thieutrieu.ui.adapter.TaskAdapter;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class MainActivitySimple extends AppCompatActivity implements TaskAdapter.OnTaskClickListener {

    private RecyclerView recyclerViewTasks;
    private TaskAdapter taskAdapter;
    private FloatingActionButton fabAddTask;
    private List<Task> tasks;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main_simple);
        
        initViews();
        setupRecyclerView();
        setupClickListeners();
        loadSampleTasks();
    }

    private void initViews() {
        recyclerViewTasks = findViewById(R.id.recyclerViewTasks);
        fabAddTask = findViewById(R.id.fabAddTask);
    }

    private void setupRecyclerView() {
        tasks = new ArrayList<>();
        taskAdapter = new TaskAdapter(tasks);
        taskAdapter.setOnTaskClickListener(this);
        
        recyclerViewTasks.setLayoutManager(new LinearLayoutManager(this));
        recyclerViewTasks.setAdapter(taskAdapter);
    }

    private void setupClickListeners() {
        fabAddTask.setOnClickListener(v -> {
            Toast.makeText(this, "Thêm task - Chức năng đang phát triển", Toast.LENGTH_SHORT).show();
        });
    }

    private void loadSampleTasks() {
        tasks.clear();
        tasks.add(new Task("Cuu lay am nhac", new Date(), null, "user1", null, "giai oan cho Jack", "work", false));
        tasks.add(new Task("Họp team", new Date(), null, "user1", null, "Họp review dự án", "work", false));
        tasks.add(new Task("Mua sắm", new Date(), null, "user1", null, "Mua đồ ăn cho tuần", "personal", false));
        
        taskAdapter.updateTasks(tasks);
    }

    @Override
    public void onTaskClick(Task task) {
        Toast.makeText(this, "Clicked: " + task.getTitle(), Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onTaskCompleteToggle(Task task, boolean isCompleted) {
        task.setCompleted(isCompleted);
        if (isCompleted) {
            task.setCompletedAt(new Date());
            Toast.makeText(this, "Đã hoàn thành: " + task.getTitle(), Toast.LENGTH_SHORT).show();
        } else {
            task.setCompletedAt(null);
            Toast.makeText(this, "Đã bỏ hoàn thành: " + task.getTitle(), Toast.LENGTH_SHORT).show();
        }
        taskAdapter.notifyDataSetChanged();
    }
}
