package com.example.thieutrieu.ui.main;

import android.Manifest;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.Insets;
import androidx.core.view.GravityCompat;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.thieutrieu.R;
import com.example.thieutrieu.data.model.Task;
import com.example.thieutrieu.ui.adapter.TaskAdapter;
import com.example.thieutrieu.ui.fragment.AddTaskFragment;
import com.example.thieutrieu.ui.fragment.MenuFragment;
import com.example.thieutrieu.utils.NotificationHelper;
import com.google.android.material.floatingactionbutton.FloatingActionButton;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class MainActivity extends AppCompatActivity implements TaskAdapter.OnTaskClickListener, AddTaskFragment.OnTaskSavedListener {

    private static final int NOTIFICATION_PERMISSION_REQUEST_CODE = 1001;

    private DrawerLayout drawerLayout;
    private RecyclerView recyclerViewTasks;
    private TaskAdapter taskAdapter;
    private FloatingActionButton fabAddTask;
    private ImageButton btnMenu;
    private EditText etSearch;
    private LinearLayout navTasks, navCalendar, navProfile;

    private List<Task> allTasks;
    private List<Task> filteredTasks;
    private NotificationHelper notificationHelper;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_main);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        try {
            initViews();
            setupRecyclerView();
            setupClickListeners();
            // setupDrawer(); // Tạm thời comment để tránh lỗi
            loadTasks();
            // requestNotificationPermission(); // Tạm thời comment để tránh lỗi
            // notificationHelper = new NotificationHelper(this); // Tạm thời comment
        } catch (Exception e) {
            e.printStackTrace();
            Toast.makeText(this, "Lỗi khởi tạo: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    private void initViews() {
        try {
            drawerLayout = findViewById(R.id.drawerLayout);
            recyclerViewTasks = findViewById(R.id.recyclerViewTasks);
            fabAddTask = findViewById(R.id.fabAddTask);
            btnMenu = findViewById(R.id.btnMenu);
            etSearch = findViewById(R.id.etSearch);
            navTasks = findViewById(R.id.navTasks);
            navCalendar = findViewById(R.id.navCalendar);
            navProfile = findViewById(R.id.navProfile);

            // Kiểm tra các view bắt buộc
            if (recyclerViewTasks == null || fabAddTask == null) {
                throw new RuntimeException("Không tìm thấy RecyclerView hoặc FAB");
            }
        } catch (Exception e) {
            e.printStackTrace();
            Toast.makeText(this, "Lỗi tìm views: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    private void setupRecyclerView() {
        allTasks = new ArrayList<>();
        filteredTasks = new ArrayList<>();
        taskAdapter = new TaskAdapter(filteredTasks);
        taskAdapter.setOnTaskClickListener(this);

        recyclerViewTasks.setLayoutManager(new LinearLayoutManager(this));
        recyclerViewTasks.setAdapter(taskAdapter);
    }

    private void setupClickListeners() {
        try {
            if (fabAddTask != null) {
                fabAddTask.setOnClickListener(v -> {
                    Toast.makeText(this, "Thêm task - Chức năng đang phát triển", Toast.LENGTH_SHORT).show();
                });
            }

            if (btnMenu != null) {
                btnMenu.setOnClickListener(v -> {
                    Toast.makeText(this, "Menu - Chức năng đang phát triển", Toast.LENGTH_SHORT).show();
                });
            }

            if (navTasks != null) {
                navTasks.setOnClickListener(v -> {
                    Toast.makeText(this, "Nhiệm vụ", Toast.LENGTH_SHORT).show();
                });
            }

            if (navCalendar != null) {
                navCalendar.setOnClickListener(v -> {
                    Toast.makeText(this, "Lịch - Chức năng đang phát triển", Toast.LENGTH_SHORT).show();
                });
            }

            if (navProfile != null) {
                navProfile.setOnClickListener(v -> {
                    Toast.makeText(this, "Cá nhân - Chức năng đang phát triển", Toast.LENGTH_SHORT).show();
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void setupDrawer() {
        // Add menu fragment to drawer
        try {
            MenuFragment menuFragment = new MenuFragment();
            getSupportFragmentManager()
                .beginTransaction()
                .replace(R.id.navigationView, menuFragment)
                .commit();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void loadTasks() {
        try {
            // Sử dụng mainFragment để lấy dữ liệu (giữ nguyên logic cũ của bạn)
            mainFragment fragment = new mainFragment();
            allTasks.clear();
            allTasks.addAll(fragment.getAllTask()); // Sử dụng method từ mainFragment

            // Thêm thêm một số task khác
            allTasks.add(new Task("Họp team", getTomorrowDate(), null, "user1", null, "Họp review dự án", "work", false));
            allTasks.add(new Task("Mua sắm", getNextWeekDate(), null, "user1", null, "Mua đồ ăn cho tuần", "personal", false));

            filterTasksForToday();

            Toast.makeText(this, "Đã tải " + allTasks.size() + " tasks", Toast.LENGTH_SHORT).show();
        } catch (Exception e) {
            e.printStackTrace();
            Toast.makeText(this, "Lỗi tải tasks: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    private Date getTomorrowDate() {
        java.util.Calendar cal = java.util.Calendar.getInstance();
        cal.add(java.util.Calendar.DAY_OF_MONTH, 1);
        cal.set(java.util.Calendar.HOUR_OF_DAY, 9);
        cal.set(java.util.Calendar.MINUTE, 0);
        return cal.getTime();
    }

    private Date getNextWeekDate() {
        java.util.Calendar cal = java.util.Calendar.getInstance();
        cal.add(java.util.Calendar.DAY_OF_MONTH, 7);
        cal.set(java.util.Calendar.HOUR_OF_DAY, 10);
        cal.set(java.util.Calendar.MINUTE, 0);
        return cal.getTime();
    }

    private void filterTasksForToday() {
        // Sử dụng logic từ mainFragment
        mainFragment fragment = new mainFragment();
        filteredTasks.clear();

        // Lấy tasks cho hôm nay từ mainFragment
        List<Task> todayTasks = fragment.getTaskForToday();
        filteredTasks.addAll(todayTasks);

        // Thêm tất cả tasks khác để demo
        filteredTasks.addAll(allTasks);

        // Loại bỏ duplicates
        List<Task> uniqueTasks = new ArrayList<>();
        for (Task task : filteredTasks) {
            if (!uniqueTasks.contains(task)) {
                uniqueTasks.add(task);
            }
        }

        taskAdapter.updateTasks(uniqueTasks);
    }

    private String getTodayDate() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        return sdf.format(new Date());
    }

    private void showAddTaskDialog() {
        AddTaskFragment addTaskFragment = new AddTaskFragment();
        addTaskFragment.setOnTaskSavedListener(this);
        addTaskFragment.show(getSupportFragmentManager(), "AddTaskFragment");
    }

    private void requestNotificationPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS)
                != PackageManager.PERMISSION_GRANTED) {
                ActivityCompat.requestPermissions(this,
                    new String[]{Manifest.permission.POST_NOTIFICATIONS},
                    NOTIFICATION_PERMISSION_REQUEST_CODE);
            }
        }
    }

    // TaskAdapter.OnTaskClickListener implementation
    @Override
    public void onTaskClick(Task task) {
        Toast.makeText(this, "Clicked: " + task.getTitle(), Toast.LENGTH_SHORT).show();
        // TODO: Open task detail dialog
    }

    @Override
    public void onTaskCompleteToggle(Task task, boolean isCompleted) {
        task.setCompleted(isCompleted);
        if (isCompleted) {
            task.setCompletedAt(new Date());
            Toast.makeText(this, "Đã hoàn thành: " + task.getTitle(), Toast.LENGTH_SHORT).show();
        } else {
            task.setCompletedAt(null);
            Toast.makeText(this, "Đã bỏ hoàn thành: " + task.getTitle(), Toast.LENGTH_SHORT).show();
        }
        taskAdapter.notifyDataSetChanged();
    }

    // AddTaskFragment.OnTaskSavedListener implementation
    @Override
    public void onTaskSaved(Task task) {
        allTasks.add(task);

        // Schedule notification for new task
        int taskId = allTasks.size() - 1;
        notificationHelper.scheduleTaskReminder(task, taskId);

        filterTasksForToday();
        Toast.makeText(this, "Đã thêm nhiệm vụ: " + task.getTitle(), Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == NOTIFICATION_PERMISSION_REQUEST_CODE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                Toast.makeText(this, "Đã cấp quyền thông báo", Toast.LENGTH_SHORT).show();
            } else {
                Toast.makeText(this, "Cần quyền thông báo để nhắc nhở nhiệm vụ", Toast.LENGTH_LONG).show();
            }
        }
    }
}