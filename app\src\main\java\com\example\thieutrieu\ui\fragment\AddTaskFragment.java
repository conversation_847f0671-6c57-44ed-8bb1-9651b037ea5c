package com.example.thieutrieu.ui.fragment;

import android.app.DatePickerDialog;
import android.app.TimePickerDialog;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import com.example.thieutrieu.R;
import com.example.thieutrieu.data.model.Task;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

public class AddTaskFragment extends DialogFragment {

    private EditText etTitle, etDescription;
    private TextView tvStartDate, tvDeadline;
    private Spinner spinnerCategory;
    private Button btnSave, btnCancel, btnSelectStartDate, btnSelectDeadline;
    
    private Date selectedStartDate, selectedDeadline;
    private OnTaskSavedListener listener;

    public interface OnTaskSavedListener {
        void onTaskSaved(Task task);
    }

    public void setOnTaskSavedListener(OnTaskSavedListener listener) {
        this.listener = listener;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_add_task, container, false);
        
        initViews(view);
        setupClickListeners();
        
        return view;
    }

    private void initViews(View view) {
        etTitle = view.findViewById(R.id.etTitle);
        etDescription = view.findViewById(R.id.etDescription);
        tvStartDate = view.findViewById(R.id.tvStartDate);
        tvDeadline = view.findViewById(R.id.tvDeadline);
        spinnerCategory = view.findViewById(R.id.spinnerCategory);
        btnSave = view.findViewById(R.id.btnSave);
        btnCancel = view.findViewById(R.id.btnCancel);
        btnSelectStartDate = view.findViewById(R.id.btnSelectStartDate);
        btnSelectDeadline = view.findViewById(R.id.btnSelectDeadline);
    }

    private void setupClickListeners() {
        btnSelectStartDate.setOnClickListener(v -> showDateTimePicker(true));
        btnSelectDeadline.setOnClickListener(v -> showDateTimePicker(false));
        
        btnSave.setOnClickListener(v -> saveTask());
        btnCancel.setOnClickListener(v -> dismiss());
    }

    private void showDateTimePicker(boolean isStartDate) {
        Calendar calendar = Calendar.getInstance();
        
        DatePickerDialog datePickerDialog = new DatePickerDialog(
            getContext(),
            (view, year, month, dayOfMonth) -> {
                calendar.set(Calendar.YEAR, year);
                calendar.set(Calendar.MONTH, month);
                calendar.set(Calendar.DAY_OF_MONTH, dayOfMonth);
                
                TimePickerDialog timePickerDialog = new TimePickerDialog(
                    getContext(),
                    (timeView, hourOfDay, minute) -> {
                        calendar.set(Calendar.HOUR_OF_DAY, hourOfDay);
                        calendar.set(Calendar.MINUTE, minute);
                        
                        Date selectedDate = calendar.getTime();
                        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault());
                        
                        if (isStartDate) {
                            selectedStartDate = selectedDate;
                            tvStartDate.setText(sdf.format(selectedDate));
                        } else {
                            selectedDeadline = selectedDate;
                            tvDeadline.setText(sdf.format(selectedDate));
                        }
                    },
                    calendar.get(Calendar.HOUR_OF_DAY),
                    calendar.get(Calendar.MINUTE),
                    true
                );
                timePickerDialog.show();
            },
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH),
            calendar.get(Calendar.DAY_OF_MONTH)
        );
        datePickerDialog.show();
    }

    private void saveTask() {
        String title = etTitle.getText().toString().trim();
        String description = etDescription.getText().toString().trim();
        
        if (title.isEmpty()) {
            Toast.makeText(getContext(), "Vui lòng nhập tiêu đề", Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (selectedStartDate == null) {
            Toast.makeText(getContext(), "Vui lòng chọn ngày bắt đầu", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // Create new task
        Task newTask = new Task(
            title,
            selectedStartDate,
            selectedDeadline,
            "user1", // Default user ID
            null, // completedAt
            description,
            "work", // Default category
            false // not completed
        );
        
        if (listener != null) {
            listener.onTaskSaved(newTask);
        }
        
        dismiss();
    }

    @Override
    public void onStart() {
        super.onStart();
        if (getDialog() != null && getDialog().getWindow() != null) {
            getDialog().getWindow().setLayout(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            );
        }
    }
}
