[{"merged": "com.example.thieutrieu.app-debug-33:/drawable_ic_launcher_background.xml.flat", "source": "com.example.thieutrieu.app-main-35:/drawable/ic_launcher_background.xml"}, {"merged": "com.example.thieutrieu.app-debug-33:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.example.thieutrieu.app-main-35:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.example.thieutrieu.app-debug-33:/layout_flagment_main.xml.flat", "source": "com.example.thieutrieu.app-main-35:/layout/flagment_main.xml"}, {"merged": "com.example.thieutrieu.app-debug-33:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.example.thieutrieu.app-main-35:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.example.thieutrieu.app-debug-33:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.example.thieutrieu.app-main-35:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.example.thieutrieu.app-debug-33:/layout_activity_main_simple.xml.flat", "source": "com.example.thieutrieu.app-main-35:/layout/activity_main_simple.xml"}, {"merged": "com.example.thieutrieu.app-debug-33:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.example.thieutrieu.app-main-35:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.example.thieutrieu.app-debug-33:/drawable_ic_notification.xml.flat", "source": "com.example.thieutrieu.app-main-35:/drawable/ic_notification.xml"}, {"merged": "com.example.thieutrieu.app-debug-33:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.example.thieutrieu.app-main-35:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.example.thieutrieu.app-debug-33:/layout_activity_main.xml.flat", "source": "com.example.thieutrieu.app-main-35:/layout/activity_main.xml"}, {"merged": "com.example.thieutrieu.app-debug-33:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.example.thieutrieu.app-main-35:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.example.thieutrieu.app-debug-33:/drawable_add_button_background.xml.flat", "source": "com.example.thieutrieu.app-main-35:/drawable/add_button_background.xml"}, {"merged": "com.example.thieutrieu.app-debug-33:/drawable_ic_launcher_foreground.xml.flat", "source": "com.example.thieutrieu.app-main-35:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.example.thieutrieu.app-debug-33:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.example.thieutrieu.app-main-35:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.example.thieutrieu.app-debug-33:/mipmap-anydpi_ic_launcher.xml.flat", "source": "com.example.thieutrieu.app-main-35:/mipmap-anydpi/ic_launcher.xml"}, {"merged": "com.example.thieutrieu.app-debug-33:/xml_backup_rules.xml.flat", "source": "com.example.thieutrieu.app-main-35:/xml/backup_rules.xml"}, {"merged": "com.example.thieutrieu.app-debug-33:/xml_data_extraction_rules.xml.flat", "source": "com.example.thieutrieu.app-main-35:/xml/data_extraction_rules.xml"}, {"merged": "com.example.thieutrieu.app-debug-33:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.example.thieutrieu.app-main-35:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.example.thieutrieu.app-debug-33:/drawable_category_button_selected.xml.flat", "source": "com.example.thieutrieu.app-main-35:/drawable/category_button_selected.xml"}, {"merged": "com.example.thieutrieu.app-debug-33:/mipmap-anydpi_ic_launcher_round.xml.flat", "source": "com.example.thieutrieu.app-main-35:/mipmap-anydpi/ic_launcher_round.xml"}, {"merged": "com.example.thieutrieu.app-debug-33:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.example.thieutrieu.app-main-35:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.example.thieutrieu.app-debug-33:/layout_fragment_add_task.xml.flat", "source": "com.example.thieutrieu.app-main-35:/layout/fragment_add_task.xml"}, {"merged": "com.example.thieutrieu.app-debug-33:/layout_item_task.xml.flat", "source": "com.example.thieutrieu.app-main-35:/layout/item_task.xml"}, {"merged": "com.example.thieutrieu.app-debug-33:/layout_fragment_menu.xml.flat", "source": "com.example.thieutrieu.app-main-35:/layout/fragment_menu.xml"}, {"merged": "com.example.thieutrieu.app-debug-33:/drawable_category_button_normal.xml.flat", "source": "com.example.thieutrieu.app-main-35:/drawable/category_button_normal.xml"}, {"merged": "com.example.thieutrieu.app-debug-33:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.example.thieutrieu.app-main-35:/mipmap-xxhdpi/ic_launcher_round.webp"}]