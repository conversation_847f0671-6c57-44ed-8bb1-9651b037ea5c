[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-debug-32:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-main-34:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-debug-32:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-main-34:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-debug-32:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-main-34:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-debug-32:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-main-34:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-debug-32:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-main-34:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-debug-32:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-main-34:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-debug-32:\\layout_flagment_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-main-34:\\layout\\flagment_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-debug-32:\\mipmap-anydpi_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-main-34:\\mipmap-anydpi\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-debug-32:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-main-34:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-debug-32:\\mipmap-anydpi_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-main-34:\\mipmap-anydpi\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-debug-32:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-main-34:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-debug-32:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-main-34:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "com.example.thieutrieu.app-debug-32:/layout_activity_main.xml.flat", "source": "com.example.thieutrieu.app-main-34:/layout/activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-debug-32:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-main-34:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-debug-32:\\drawable_category_button_selected.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-main-34:\\drawable\\category_button_selected.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-debug-32:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-main-34:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-debug-32:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-main-34:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-debug-32:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-main-34:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-debug-32:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-main-34:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-debug-32:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.thieutrieu.app-main-34:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}]