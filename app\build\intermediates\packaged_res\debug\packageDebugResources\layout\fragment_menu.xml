<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="280dp"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#FFFFFF"
    android:padding="0dp">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="160dp"
        android:orientation="vertical"
        android:background="#4CAF50"
        android:padding="24dp"
        android:gravity="bottom">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Todo App"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="#FFFFFF" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="<PERSON>u<PERSON><PERSON> lý công việc hi<PERSON>u quả"
            android:textSize="14sp"
            android:textColor="#E8F5E8"
            android:layout_marginTop="4dp" />

    </LinearLayout>

    <!-- Menu Items -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingTop="16dp">

            <!-- Nhiệm vụ -->
            <LinearLayout
                android:id="@+id/menuTasks"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingStart="24dp"
                android:paddingEnd="24dp"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@android:drawable/ic_menu_agenda"
                    android:tint="#4CAF50" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Nhiệm vụ"
                    android:textSize="16sp"
                    android:textColor="#333333"
                    android:layout_marginStart="16dp" />

            </LinearLayout>

            <!-- Lịch -->
            <LinearLayout
                android:id="@+id/menuCalendar"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingStart="24dp"
                android:paddingEnd="24dp"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@android:drawable/ic_menu_today"
                    android:tint="#666666" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Lịch"
                    android:textSize="16sp"
                    android:textColor="#333333"
                    android:layout_marginStart="16dp" />

            </LinearLayout>

            <!-- Cá nhân -->
            <LinearLayout
                android:id="@+id/menuProfile"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingStart="24dp"
                android:paddingEnd="24dp"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@android:drawable/ic_menu_myplaces"
                    android:tint="#666666" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Cá nhân"
                    android:textSize="16sp"
                    android:textColor="#333333"
                    android:layout_marginStart="16dp" />

            </LinearLayout>

            <!-- Divider -->
            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#E0E0E0"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp" />

            <!-- Quản lý danh mục -->
            <LinearLayout
                android:id="@+id/menuCategories"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingStart="24dp"
                android:paddingEnd="24dp"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@android:drawable/ic_menu_sort_by_size"
                    android:tint="#666666" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Quản lý danh mục"
                    android:textSize="16sp"
                    android:textColor="#333333"
                    android:layout_marginStart="16dp" />

            </LinearLayout>

            <!-- Cài đặt -->
            <LinearLayout
                android:id="@+id/menuSettings"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingStart="24dp"
                android:paddingEnd="24dp"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@android:drawable/ic_menu_preferences"
                    android:tint="#666666" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Cài đặt"
                    android:textSize="16sp"
                    android:textColor="#333333"
                    android:layout_marginStart="16dp" />

            </LinearLayout>

            <!-- Về ứng dụng -->
            <LinearLayout
                android:id="@+id/menuAbout"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingStart="24dp"
                android:paddingEnd="24dp"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@android:drawable/ic_menu_info_details"
                    android:tint="#666666" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Về ứng dụng"
                    android:textSize="16sp"
                    android:textColor="#333333"
                    android:layout_marginStart="16dp" />

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

</LinearLayout>
