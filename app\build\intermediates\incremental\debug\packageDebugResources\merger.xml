<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\ThieuTrieu\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\ThieuTrieu\app\src\main\res"><file name="ic_launcher_background" path="C:\Users\<USER>\AndroidStudioProjects\ThieuTrieu\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\ThieuTrieu\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="activity_main" path="C:\Users\<USER>\AndroidStudioProjects\ThieuTrieu\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\ThieuTrieu\app\src\main\res\mipmap-anydpi\ic_launcher.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\ThieuTrieu\app\src\main\res\mipmap-anydpi\ic_launcher_round.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\ThieuTrieu\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\ThieuTrieu\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\ThieuTrieu\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\ThieuTrieu\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\ThieuTrieu\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\ThieuTrieu\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\ThieuTrieu\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\ThieuTrieu\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\ThieuTrieu\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\ThieuTrieu\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\AndroidStudioProjects\ThieuTrieu\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="C:\Users\<USER>\AndroidStudioProjects\ThieuTrieu\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">ThieuTrieu</string></file><file path="C:\Users\<USER>\AndroidStudioProjects\ThieuTrieu\app\src\main\res\values\themes.xml" qualifiers=""><style name="Base.Theme.ThieuTrieu" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style><style name="Theme.ThieuTrieu" parent="Base.Theme.ThieuTrieu"/></file><file path="C:\Users\<USER>\AndroidStudioProjects\ThieuTrieu\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Base.Theme.ThieuTrieu" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style></file><file name="backup_rules" path="C:\Users\<USER>\AndroidStudioProjects\ThieuTrieu\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\AndroidStudioProjects\ThieuTrieu\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="category_button_selected" path="C:\Users\<USER>\AndroidStudioProjects\ThieuTrieu\app\src\main\res\drawable\category_button_selected.xml" qualifiers="" type="drawable"/><file name="flagment_main" path="C:\Users\<USER>\AndroidStudioProjects\ThieuTrieu\app\src\main\res\layout\flagment_main.xml" qualifiers="" type="layout"/><file name="ic_notification" path="C:\Users\<USER>\AndroidStudioProjects\ThieuTrieu\app\src\main\res\drawable\ic_notification.xml" qualifiers="" type="drawable"/><file name="fragment_add_task" path="C:\Users\<USER>\AndroidStudioProjects\ThieuTrieu\app\src\main\res\layout\fragment_add_task.xml" qualifiers="" type="layout"/><file name="fragment_menu" path="C:\Users\<USER>\AndroidStudioProjects\ThieuTrieu\app\src\main\res\layout\fragment_menu.xml" qualifiers="" type="layout"/><file name="item_task" path="C:\Users\<USER>\AndroidStudioProjects\ThieuTrieu\app\src\main\res\layout\item_task.xml" qualifiers="" type="layout"/><file name="add_button_background" path="C:\Users\<USER>\AndroidStudioProjects\ThieuTrieu\app\src\main\res\drawable\add_button_background.xml" qualifiers="" type="drawable"/><file name="category_button_normal" path="C:\Users\<USER>\AndroidStudioProjects\ThieuTrieu\app\src\main\res\drawable\category_button_normal.xml" qualifiers="" type="drawable"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\ThieuTrieu\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\ThieuTrieu\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\ThieuTrieu\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\ThieuTrieu\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>