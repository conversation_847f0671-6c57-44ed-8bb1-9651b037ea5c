package com.example.thieutrieu.ui.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import com.example.thieutrieu.R;

public class MenuFragment extends Fragment {

    private LinearLayout menuTasks, menuCalendar, menuProfile, menuCategories, menuSettings, menuAbout;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_menu, container, false);
        
        initViews(view);
        setupClickListeners();
        
        return view;
    }

    private void initViews(View view) {
        menuTasks = view.findViewById(R.id.menuTasks);
        menuCalendar = view.findViewById(R.id.menuCalendar);
        menuProfile = view.findViewById(R.id.menuProfile);
        menuCategories = view.findViewById(R.id.menuCategories);
        menuSettings = view.findViewById(R.id.menuSettings);
        menuAbout = view.findViewById(R.id.menuAbout);
    }

    private void setupClickListeners() {
        menuTasks.setOnClickListener(v -> {
            Toast.makeText(getContext(), "Nhiệm vụ", Toast.LENGTH_SHORT).show();
            // Navigate to tasks
        });

        menuCalendar.setOnClickListener(v -> {
            Toast.makeText(getContext(), "Lịch", Toast.LENGTH_SHORT).show();
            // Navigate to calendar
        });

        menuProfile.setOnClickListener(v -> {
            Toast.makeText(getContext(), "Cá nhân", Toast.LENGTH_SHORT).show();
            // Navigate to profile
        });

        menuCategories.setOnClickListener(v -> {
            Toast.makeText(getContext(), "Quản lý danh mục", Toast.LENGTH_SHORT).show();
            // Navigate to categories management
        });

        menuSettings.setOnClickListener(v -> {
            Toast.makeText(getContext(), "Cài đặt", Toast.LENGTH_SHORT).show();
            // Navigate to settings
        });

        menuAbout.setOnClickListener(v -> {
            Toast.makeText(getContext(), "Về ứng dụng", Toast.LENGTH_SHORT).show();
            // Show about dialog
        });
    }
}
