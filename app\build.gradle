plugins {
    alias(libs.plugins.android.application)
}

android {
    namespace 'com.example.thieutrieu'
    compileSdk 35

    defaultConfig {
        applicationId "com.example.thieutrieu"
        minSdk 28
        targetSdk 35
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {

    implementation libs.appcompat
    implementation libs.material
    implementation libs.activity
    implementation libs.constraintlayout

    // RecyclerView
    implementation 'androidx.recyclerview:recyclerview:1.3.2'

    // DrawerLayout
    implementation 'androidx.drawerlayout:drawerlayout:1.2.0'

    // Fragment
    implementation 'androidx.fragment:fragment:1.6.2'

    // CardView
    implementation 'androidx.cardview:cardview:1.0.0'

    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core
}