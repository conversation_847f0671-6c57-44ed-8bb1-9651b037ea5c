package com.example.thieutrieu.data.model;

import java.util.Date;
public class Task {
    private String title;
    private Date startDate;
    private  Date deadLine;
    private String description;
    private String userId;
    private String categoryId;
    private Date completedAt;
    private boolean completed;

    public Task(String title, Date startDate,Date deadLine, String userId,Date completedAt, String description,String categoryId,boolean completed ){
            this.categoryId = categoryId;
            this.deadLine = deadLine;
            this.title = title;
            this.userId = userId;
            this.startDate = startDate;
            this.completed = completed;
            this.completedAt = completedAt;
            this.description = description;
    }

    public String getTitle() {return title;}
    public String getDescription(){return description;}
    public Date getStartDate(){ return startDate; }
    public Date getDeadLine(){return deadLine; }
    public String getUserId(){return userId;}
    public String getCategoryId(){return categoryId;}
    public boolean isCompleted(){return completed;}
    public Date getCompletedAt(){return completedAt;}
    public void setCompleted(boolean completed){
        this.completed = completed;
    }

    public void setCompletedAt(Date completedAt){
        this.completedAt = completedAt;
    }
}
