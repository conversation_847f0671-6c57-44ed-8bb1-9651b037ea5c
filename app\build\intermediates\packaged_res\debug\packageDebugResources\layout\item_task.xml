<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    android:background="#FFFFFF">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <CheckBox
            android:id="@+id/cbCompleted"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Task Title"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="#333333" />

            <TextView
                android:id="@+id/tvDescription"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Task Description"
                android:textSize="14sp"
                android:textColor="#666666"
                android:layout_marginTop="4dp"
                android:maxLines="2"
                android:ellipsize="end" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="8dp">

                <TextView
                    android:id="@+id/tvStartDate"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="01/01/2024 09:00"
                    android:textSize="12sp"
                    android:textColor="#999999"
                    android:drawableStart="@android:drawable/ic_menu_recent_history"
                    android:drawablePadding="4dp" />

                <TextView
                    android:id="@+id/tvCategory"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Công việc"
                    android:textSize="12sp"
                    android:textColor="#FFFFFF"
                    android:background="#4CAF50"
                    android:paddingStart="8dp"
                    android:paddingEnd="8dp"
                    android:paddingTop="2dp"
                    android:paddingBottom="2dp"
                    android:layout_marginStart="8dp" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
