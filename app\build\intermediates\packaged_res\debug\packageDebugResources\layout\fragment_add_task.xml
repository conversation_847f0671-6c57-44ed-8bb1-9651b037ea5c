<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="#FFFFFF">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Thêm nhiệm vụ mới"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="#333333"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <!-- Title -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp">

        <EditText
            android:id="@+id/etTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Tiêu đề nhiệm vụ"
            android:inputType="text"
            android:maxLines="1" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Description -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp">

        <EditText
            android:id="@+id/etDescription"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Mô tả (tùy chọn)"
            android:inputType="textMultiLine"
            android:maxLines="3" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Start Date -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Ngày bắt đầu:"
            android:textSize="16sp"
            android:textColor="#333333" />

        <Button
            android:id="@+id/btnSelectStartDate"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:text="Chọn ngày"
            android:textSize="12sp"
            android:layout_marginStart="8dp" />

    </LinearLayout>

    <TextView
        android:id="@+id/tvStartDate"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Chưa chọn"
        android:textSize="14sp"
        android:textColor="#666666"
        android:layout_marginBottom="16dp"
        android:padding="8dp"
        android:background="#F5F5F5" />

    <!-- Deadline -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Hạn chót (tùy chọn):"
            android:textSize="16sp"
            android:textColor="#333333" />

        <Button
            android:id="@+id/btnSelectDeadline"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:text="Chọn ngày"
            android:textSize="12sp"
            android:layout_marginStart="8dp" />

    </LinearLayout>

    <TextView
        android:id="@+id/tvDeadline"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Chưa chọn"
        android:textSize="14sp"
        android:textColor="#666666"
        android:layout_marginBottom="16dp"
        android:padding="8dp"
        android:background="#F5F5F5" />

    <!-- Category -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Danh mục:"
        android:textSize="16sp"
        android:textColor="#333333"
        android:layout_marginBottom="8dp" />

    <Spinner
        android:id="@+id/spinnerCategory"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginBottom="24dp" />

    <!-- Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end">

        <Button
            android:id="@+id/btnCancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Hủy"
            android:textColor="#666666"
            android:background="?attr/selectableItemBackground"
            android:layout_marginEnd="16dp" />

        <Button
            android:id="@+id/btnSave"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Lưu"
            android:textColor="#FFFFFF"
            android:backgroundTint="#4CAF50" />

    </LinearLayout>

</LinearLayout>
