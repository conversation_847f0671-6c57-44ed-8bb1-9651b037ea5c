<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawerLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.main.MainActivity">

    <!-- Main Content -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#F5F5F5">

    <!-- Header với nút menu và search -->
    <LinearLayout
        android:id="@+id/headerLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical"
        android:background="#FFFFFF"
        android:elevation="4dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- Nút menu hamburger -->
        <ImageButton
            android:id="@+id/btnMenu"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@android:drawable/ic_menu_sort_by_size"
            android:contentDescription="Menu"
            android:layout_marginEnd="16dp" />

        <!-- Search bar -->
        <androidx.cardview.widget.CardView
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            app:cardCornerRadius="20dp"
            app:cardElevation="2dp"
            android:layout_marginEnd="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingStart="16dp"
                android:paddingEnd="16dp">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@android:drawable/ic_menu_search"
                    android:layout_marginEnd="8dp" />

                <EditText
                    android:id="@+id/etSearch"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@null"
                    android:hint="Tìm kiếm task..."
                    android:textSize="14sp"
                    android:inputType="text" />

            </LinearLayout>
        </androidx.cardview.widget.CardView>

    </LinearLayout>

    <!-- Category filter section -->
    <LinearLayout
        android:id="@+id/categoryLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        app:layout_constraintTop_toBottomOf="@id/headerLayout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <HorizontalScrollView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:scrollbars="none">

            <LinearLayout
                android:id="@+id/categoryContainer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <!-- Category buttons sẽ được thêm động -->
                <Button
                    android:id="@+id/btnCategoryAll"
                    android:layout_width="wrap_content"
                    android:layout_height="36dp"
                    android:text="Tất cả"
                    android:textSize="12sp"
                    android:background="@drawable/category_button_selected"
                    android:textColor="#FFFFFF"
                    android:layout_marginEnd="8dp"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp" />

                <Button
                    android:id="@+id/btnCategoryWork"
                    android:layout_width="wrap_content"
                    android:layout_height="36dp"
                    android:text="Công việc"
                    android:textSize="12sp"
                    android:background="@drawable/category_button_normal"
                    android:textColor="#666666"
                    android:layout_marginEnd="8dp"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp" />

                <Button
                    android:id="@+id/btnCategoryPersonal"
                    android:layout_width="wrap_content"
                    android:layout_height="36dp"
                    android:text="Cá nhân"
                    android:textSize="12sp"
                    android:background="@drawable/category_button_normal"
                    android:textColor="#666666"
                    android:layout_marginEnd="8dp"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp" />

            </LinearLayout>
        </HorizontalScrollView>

        <!-- Nút thêm category -->
        <ImageButton
            android:id="@+id/btnAddCategory"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:background="@drawable/add_button_background"
            android:src="@android:drawable/ic_input_add"
            android:contentDescription="Thêm category"
            android:layout_marginStart="8dp" />

    </LinearLayout>

    <!-- Date filter -->
    <LinearLayout
        android:id="@+id/dateFilterLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:paddingBottom="8dp"
        android:gravity="center_vertical"
        app:layout_constraintTop_toBottomOf="@id/categoryLayout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Hôm nay"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="#333333" />

        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />

        <ImageButton
            android:id="@+id/btnDateFilter"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@android:drawable/ic_menu_today"
            android:contentDescription="Lọc theo ngày" />

    </LinearLayout>

    <!-- Task list -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewTasks"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:padding="16dp"
        android:clipToPadding="false"
        app:layout_constraintTop_toBottomOf="@id/dateFilterLayout"
        app:layout_constraintBottom_toTopOf="@id/bottomNavigation"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Floating Action Button để thêm task -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabAddTask"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:src="@android:drawable/ic_input_add"
        android:contentDescription="Thêm task"
        app:layout_constraintBottom_toTopOf="@id/bottomNavigation"
        app:layout_constraintEnd_toEndOf="parent"
        app:backgroundTint="#4CAF50" />

    <!-- Bottom Navigation -->
    <LinearLayout
        android:id="@+id/bottomNavigation"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:orientation="horizontal"
        android:background="#FFFFFF"
        android:elevation="8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- Nhiệm vụ -->
        <LinearLayout
            android:id="@+id/navTasks"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:background="?attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@android:drawable/ic_menu_agenda"
                android:tint="#4CAF50" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Nhiệm vụ"
                android:textSize="10sp"
                android:textColor="#4CAF50"
                android:layout_marginTop="2dp" />

        </LinearLayout>
09
        <!-- Lịch -->
        <LinearLayout
            android:id="@+id/navCalendar"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:background="?attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@android:drawable/ic_menu_today"
                android:tint="#999999" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Lịch"
                android:textSize="10sp"
                android:textColor="#999999"
                android:layout_marginTop="2dp" />

        </LinearLayout>

        <!-- Cá nhân -->
        <LinearLayout
            android:id="@+id/navProfile"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:background="?attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@android:drawable/ic_menu_myplaces"
                android:tint="#999999" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Cá nhân"
                android:textSize="10sp"
                android:textColor="#999999"
                android:layout_marginTop="2dp" />

        </LinearLayout>

    </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Navigation Drawer -->
    <FrameLayout
        android:id="@+id/navigationView"
        android:layout_width="280dp"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:background="#FFFFFF" />

</androidx.drawerlayout.widget.DrawerLayout>