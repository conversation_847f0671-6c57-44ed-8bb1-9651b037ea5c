package com.example.thieutrieu.utils;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

public class TaskReminderReceiver extends BroadcastReceiver {
    
    @Override
    public void onReceive(Context context, Intent intent) {
        String taskTitle = intent.getStringExtra("task_title");
        String taskDescription = intent.getStringExtra("task_description");
        int taskId = intent.getIntExtra("task_id", 0);
        
        NotificationHelper notificationHelper = new NotificationHelper(context);
        notificationHelper.showTaskNotification(taskTitle, taskDescription, taskId);
    }
}
